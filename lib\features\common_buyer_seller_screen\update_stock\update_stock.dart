import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/update_stock/update-stock_bloc.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class UpdateStock extends StatefulWidget {
  final Product? product;
  final Product? singleProduct;
  const UpdateStock({Key? key,  this.product, this.singleProduct}) : super(key: key);

  @override
  State<UpdateStock> createState() => _UpdateStockState();
}

class _UpdateStockState extends State<UpdateStock> {
  //region Bloc
  late  UpdateStockBloc updateStockBloc;
  //endregion
  //region init
  @override
  void initState() {
    updateStockBloc = UpdateStockBloc(context,widget.product,widget.singleProduct);
    updateStockBloc.init();
    super.initState();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
Widget body(){
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 32,vertical: 34),

      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [

          Padding(
            padding: const EdgeInsets.only(bottom: 30),
            child: appText('Current stock : ${widget.product!.variants![0]['stock']??widget.singleProduct!.variants![0]['stock']}',color: AppColors.appBlack,fontSize: 16,fontWeight: FontWeight.w600,
              fontFamily: AppConstants.rRegular,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: colorFilledTextField(
                context: context,
                textFieldCtrl: updateStockBloc.updateTextCtrl,
                hintText: "",
                textFieldMaxLine: 1,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                regExp: AppConstants.onlyInt,
                fieldTextCapitalization: TextCapitalization.none,
                maxCharacter: 10),
          ),
          verticalSizedBox(20),
          Row(
            children: [
              button(buttonName:"Add",
              onPress: (){
                updateStockBloc.updateStock(productReference:widget.product!.productReference?? widget.singleProduct!.productReference!, inStock: widget.product!.variants![0]['stock']?? widget.singleProduct!.variants![0]['stock']!,isAdd: true);
              }
              ),
              horizontalSizedBox(10),
              button(buttonName:"Remove",onPress: (){
                updateStockBloc.updateStock(productReference:widget.product!.productReference?? widget.singleProduct!.productReference!, inStock: widget.product!.variants![0]['stock']?? widget.singleProduct!.variants![0]['stock']!,isRemove: true);

              }),
              horizontalSizedBox(10),
              button(buttonName:"Set",
              onPress: (){
                updateStockBloc.updateStock(productReference:widget.product!.productReference?? widget.singleProduct!.productReference!, inStock: widget.product!.variants![0]['stock']?? widget.singleProduct!.variants![0]['stock']!,isSet: true);

              }
              ),

            ],
          )


        ],
      ),
    );
}
//endregion


//region Buttons
Widget button({required buttonName,required dynamic onPress}){
    return Expanded(
      child: InkWell(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          child: Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 13,horizontal: 15),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.textFieldFill1
            ),
            child: Text(
              buttonName,
              style: AppTextStyle.button2Bold(textColor: AppColors.appBlack)
            ),
          ),
          onTap: () {
            onPress();
          }),
    );
}
//endregion




}

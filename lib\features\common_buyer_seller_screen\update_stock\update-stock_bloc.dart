
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/edit_product_image/edit_product_image.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class UpdateStockBloc {
  // region Common Variables
  BuildContext context;
  final Product? product;
  final Product? singleProduct;
  late EditProductAndImageServices editProductAndImageServices;

  // endregion

  //region Text Controller
  final updateTextCtrl = TextEditingController();

  //endregion

  //region Controller
  // final singleProductViewCtrl = StreamController<BuyerViewProductState>.broadcast();
  //endregion

  // region | Constructor |
  UpdateStockBloc(this.context, this.product, this.singleProduct);

  // endregion

  // region Init
  void init() {
    editProductAndImageServices = EditProductAndImageServices();
  }

// endregion

  // addToCart(int storeId,int productId,)async{
  updateStock({
    required String productReference,
    required int inStock,
    bool isAdd = false,
    bool isRemove = false,
    bool isSet = false,
  }) async {
    try {
      //If field is empty
      if(updateTextCtrl.text.isEmpty){
        return;
      }
      ///Is add
      if(isAdd){
        inStock = inStock + int.parse(updateTextCtrl.text);
      }
      ///Remove
      else if(isRemove){
        inStock = inStock - int.parse(updateTextCtrl.text);
        //If stock goes under 0 then set to 0
        if(inStock<0){
          inStock = 0;
        }
      }
      ///Set
      else if(isSet){
        inStock = int.parse(updateTextCtrl.text);
      }

      editProductAndImageServices.updateStock(productReference: productReference, inStock: inStock);

      //Update local data
      if(product != null){
        product!.variants![0]['stock'] = inStock;

      }
      else{
        singleProduct!.variants![0]['stock'] = inStock;
      }
      //Stock updated
      CommonMethods.toastMessage(AppStrings.stockUpdated, context);

      //Close screen
      Navigator.pop(context, inStock);
      // buyerViewProductImageCtrl.sink.add(BuyerViewProductState.Success);
    } on ApiErrorResponseMessage {
     CommonMethods.toastMessage(AppStrings.unableToUpdateStocks, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.unableToUpdateStocks, context);

      return;
    }
  }
//endregion

}

import 'package:swadesic/model/store_product_response/store_product_response.dart';

class LeanStoreProductResponse {
  String? message;
  List<LeanProduct>? data;

  LeanStoreProductResponse({this.message, this.data});

  LeanStoreProductResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['data'] != null) {
      data = <LeanProduct>[];
      json['data'].forEach((v) {
        data!.add(LeanProduct.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LeanProduct {
  String? productReference;
  String? productName;
  String? brandName;
  String? productCategory;
  int? mrpPrice;
  int? sellingPrice;
  List<LeanProductImage>? productImages;

  LeanProduct({
    this.productReference,
    this.productName,
    this.brandName,
    this.productCategory,
    this.mrpPrice,
    this.sellingPrice,
    this.productImages,
  });

  LeanProduct.fromJson(Map<String, dynamic> json) {
    productReference = json['product_reference'];
    productName = json['product_name'];
    brandName = json['brand_name'];
    productCategory = json['product_category'];
    mrpPrice = json['mrp_price'];
    sellingPrice = json['selling_price'];

    if (json['product_images'] != null) {
      productImages = <LeanProductImage>[];
      json['product_images'].forEach((v) {
        productImages!.add(LeanProductImage.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['product_reference'] = productReference;
    data['product_name'] = productName;
    data['brand_name'] = brandName;
    data['product_category'] = productCategory;
    data['mrp_price'] = mrpPrice;
    data['selling_price'] = sellingPrice;
    if (productImages != null) {
      data['product_images'] = productImages!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // Convert LeanProduct to Product for compatibility with existing code
  Product toProduct() {
    List<ProdImages> prodImages = [];
    if (productImages != null && productImages!.isNotEmpty) {
      prodImages = productImages!
          .map((img) => ProdImages(
                productimageid: img.productimageid,
                reorder: img.reorder,
                productImage: img.productImage,
              ))
          .toList();
    }

    return Product(
      productReference: productReference,
      productName: productName,
      brandName: brandName,
      productCategory: productCategory,
      // mrpPrice: mrpPrice,
      // sellingPrice: sellingPrice,
      prodImages: prodImages,
      // Default values for required fields
      // inStock: 1, // Assuming in stock by default
      isDeleted: false,
      // Include disclaimerMessage if it exists in the API response
      // This will be populated from the backend
      disclaimerMessage: null, // Will be populated from the backend when needed
    );
  }
}

class LeanProductImage {
  int? productimageid;
  int? reorder;
  bool? isDeleted;
  String? productImage;

  LeanProductImage({
    this.productimageid,
    this.reorder,
    this.isDeleted,
    this.productImage,
  });

  LeanProductImage.fromJson(Map<String, dynamic> json) {
    productimageid = json['productimageid'];
    reorder = json['reorder'];
    isDeleted = json['is_deleted'];
    productImage = json['product_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productimageid'] = productimageid;
    data['reorder'] = reorder;
    data['is_deleted'] = isDeleted;
    data['product_image'] = productImage;
    return data;
  }
}

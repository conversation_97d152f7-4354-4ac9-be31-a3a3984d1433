import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/seller/add_product/add_product_screen.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_details/edit_product_details_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/getProduct_image_response/get_seller_product_detail.dart';
import 'package:swadesic/model/hide_delete_response/delete_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart'
    as products;
import 'package:swadesic/services/edit_product_image/edit_product_image.dart';
import 'package:swadesic/services/get_product_and_image/get_product_and_image.dart';
import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

enum EditProductState { Loading, Success, Failed, Empty }

class EditProductBloc {
  // region Common Variables
  BuildContext context;
  List drawerOptionsList = [
    "update stock",
    "edit one by one",
    "hide products",
    "delete products"
  ];
  bool drawerVisibility = false;
  final int storeId;
  final String storeRef;
  bool isMultiSelect = false;

  List<String> productReferenceList = [];

  ///Get Store Products
  //  late StoreProductResponse storeProductResponse;
  //    late  List<ProductList> productList;
  late StoreProductServices storeProductServices;
  late products.StoreProductResponse storeProductResponse;
  late List<products.Product> searchProductList = [];

  ///Delete and Hide
  late SellerHideDeleteService sellerHideDeleteService;
  late HideDeleteApiResponse hideDeleteApiResponse;

  ///Get Only Product
  late ProductAndImageServices productAndImageServices;
  late GetOnlyProductDetail getOnlyProductResponse;

  ///Edit Product
  late EditProductAndImageServices editProductAndImageServices;

  // Get Store Hidden Products
  // late StoreHiddenProductResponse storeHiddenProductResponse;
  // endregion

  //region Controller
  final editProductCtrl = StreamController<EditProductState>.broadcast();
  final selectCtrl = StreamController<bool>.broadcast();
  final drawerCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Editing Controller
  TextEditingController searchTextCtrl = TextEditingController();
  TextEditingController stockTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  EditProductBloc(this.context, this.storeId, this.storeRef);

  // endregion

  // region Init
  Future<void> init() async {
    //Clear All Product in Product List
    searchProductList.clear();

    ///Get Store All Products List
    storeProductServices = StoreProductServices();
    storeProductResponse = products.StoreProductResponse();

    ///Delete and Hide
    sellerHideDeleteService = SellerHideDeleteService();

    ///Get only Product Detail
    productAndImageServices = ProductAndImageServices();

    ///Edit Stock
    editProductAndImageServices = EditProductAndImageServices();
    getProductApiCall();
    // sellerHideDeleteService = SellerHideDeleteService();
  }

// endregion

  //region On long press product
  void onLongPressProduct({required String productReference}) {
    productReferenceList.add(productReference);
    isMultiSelect = true;
    selectCtrl.sink.add(true);
  }

  //endregion

  //region OnChange Search Product
  void onChangeSearchField(String value) {
    //print(value);
    searchProductList.clear();
    for (var data in storeProductResponse.data!) {
      if (data.productName!.toLowerCase().contains(value.toLowerCase()) ||
          data.storehandle!.toLowerCase().contains(value.toLowerCase()) ||
          data.brandName!.toLowerCase().contains(value.toLowerCase()))
        searchProductList.add(data);
    }

    // set state
    editProductCtrl.sink.add(EditProductState.Success);
  }

  //endregion

  //region Get Product List Api Call
  Future<void> getProductApiCall() async {
    try {
      //editProductCtrl.sink.add(EditProductState.Loading);
      storeProductResponse = await storeProductServices.getBuyerStoreProduct(
          offset: 0,
          limit: 1000,
          storeReference: AppConstants.appData.storeReference!);
      //Clear list
      searchProductList.clear();
      //Add new data to list
      searchProductList.addAll(storeProductResponse.data!);
      //Check is it empty
      if (searchProductList.isEmpty) {
        return editProductCtrl.sink.add(EditProductState.Empty);
      }
      //print(searchProductList.first.productReference);
      editProductCtrl.sink.add(EditProductState.Success);
    } on ApiErrorResponseMessage {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion

  //region Go to Add Product
  void goToAddProduct() {
    var screen = AddProductScreen(
      storeId: storeId,
      storeReference: storeRef,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      getProductApiCall();
    });
  }

  //endregion

  //region Get Hidden Product List Api Call
  // void getHiddenProductApiCall()async{
  //   try{
  //     editProductCtrl.sink.add(EditProductState.Loading);
  //
  //     storeHiddenProductResponse = await storeProductServices.getStoreHiddenProduct(1);
  //     editProductCtrl.sink.add(EditProductState.Success);
  //   }
  //   on ApiErrorResponseMessage catch(error){
  //     editProductCtrl.sink.add(EditProductState.Failed);
  //     var snackBar = SnackBar(content: Text(error.message.toString()));
  //     ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   }
  //   catch(error){
  //     editProductCtrl.sink.add(EditProductState.Failed);
  //     var snackBar = SnackBar(content: Text(error.toString()));
  //     ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   }
  //
  //
  // }

//endregion

  //region On Tap Product
  void onTapProduct(String productReference) {
    if (productReferenceList.contains(productReference)) {
      productReferenceList.remove(productReference);
      //If all empty then reset to single tap status
      if (productReferenceList.isEmpty) {
        isMultiSelect = false;
      }
      selectCtrl.sink.add(true);
    } else {
      productReferenceList.add(productReference);
      selectCtrl.sink.add(true);
    }
    //print(productReferenceList.toString());
  }

  //endregion

  //region On Tap Drawer
  onTapDrawer() {
    if (productReferenceList.isEmpty) {
      editProductCtrl.sink.add(EditProductState.Success);
      CommonMethods.toastMessage(
          "Please select any product to continue", context);
      return;
    }
    drawerVisibility = !drawerVisibility;
    drawerCtrl.sink.add(drawerVisibility);
  }

  //endregion

  //region Go to Edit Product Details
  void goToEditProductDetails() {
    var screen = EditProductDetailsScreen(
      storeId: storeId,
      productReferenceList: productReferenceList,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //Clear selected products
      productReferenceList.clear();
      isMultiSelect = false;
      selectCtrl.sink.add(true);

      // If we got updated product data back, update the product in the list
      if (value != null && value is products.Product) {
        updateProductInList(value);
      }
    });
  }

  //endregion

  //region On tap delete
  void onTapDelete() {
    CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          onTapSave: (value) {
            //Reset all
            isMultiSelect = false;
            selectCtrl.sink.add(true);
            productReferenceList.clear();
            editProductCtrl.sink.add(EditProductState.Success);
            // Navigator.pop(context);
            // Navigator.pop(context);
          },
          onTapCancel: (data) {
            deleteApiCall();
          },
          firstButtonName: AppStrings.cancel,
          secondButtonName: AppStrings.delete,
          popPreviousScreen: false,
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.doYouReallyWantsToDelete,
        ));
  }

  //endregion

  //region Delete Api Call
  void deleteApiCall() async {
    try {
      for (int i = 0; i < productReferenceList.length; i++) {
        sellerHideDeleteService.deleteProduct(productReferenceList[i]);
        searchProductList.removeWhere(
            (element) => element.productReference == productReferenceList[i]);
      }
      CommonMethods.toastMessage(
          "${productReferenceList.length} Product deleted", context);
      isMultiSelect = false;
      productReferenceList.clear();
      selectCtrl.sink.add(true);
      editProductCtrl.sink.add(EditProductState.Success);
    } on ApiErrorResponseMessage {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion

  //region Hide Api Call
  void hideApiCall() async {
    try {
      for (int i = 0; i < productReferenceList.length; i++) {
        sellerHideDeleteService.hideProduct(productReferenceList[i]);
        searchProductList.removeWhere(
            (element) => element.productReference == productReferenceList[i]);
      }
      CommonMethods.toastMessage(
          "${productReferenceList.length} Product hidden", context);
      productReferenceList.clear();
      editProductCtrl.sink.add(EditProductState.Success);
    } on ApiErrorResponseMessage {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion

  //region Update Stock Dialog
  void updateStockDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Stack(
              alignment: Alignment.center,
              children: [
                const Text("Update stock"),
                Align(
                    alignment: Alignment.centerRight,
                    child: SvgPicture.asset(
                      AppImages.exclamation,
                      fit: BoxFit.cover,
                    ))
              ],
            ),
            titleTextStyle: const TextStyle(
                color: AppColors.writingColor2,
                fontSize: 16,
                fontFamily: "LatoSemiBold",
                fontWeight: FontWeight.w600),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: colorFilledTextField(
                      context: context,
                      textFieldCtrl: stockTextCtrl,
                      hintText: "",
                      textFieldMaxLine: 1,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.done,
                      regExp: AppConstants.onlyInt,
                      fieldTextCapitalization: TextCapitalization.none,
                      maxCharacter: 10),
                  // child: TextFormField(
                  //   controller:stockText,
                  //   keyboardType: TextInputType.number,
                  //
                  //   maxLines: 1,
                  //   // readOnly: true,
                  //
                  //   style: TextStyle(
                  //
                  //       fontSize: 14,
                  //       fontWeight: FontWeight.w400,
                  //       color: AppColors.appBlack),
                  //   decoration: InputDecoration(
                  //     // prefixIcon: Padding(
                  //     //   padding: EdgeInsets.symmetric(horizontal: 11.73.h),
                  //     //   child: SvgPicture.asset(AppImages.searchBarIcon,fit: BoxFit.contain,color: AppColors.appBlack7,),
                  //     // ),
                  //     // suffixIcon: CupertinoButton(
                  //     //     padding: EdgeInsets.zero,
                  //     //     onPressed: (){
                  //     //       Navigator.pop(context);
                  //     //     },
                  //     //     child: Icon(Icons.done,color: AppColors.appBlack5,size: 25,)),
                  //     filled: true,
                  //
                  //
                  //
                  //     //contentPadding: EdgeInsets.symmetric(vertical: 19.53.h),
                  //
                  //
                  //     fillColor: AppColors.lightWhite,
                  //
                  //
                  //     isDense: true,
                  //
                  //     // hintText: "search products, stores & your friends",
                  //     // hintStyle: TextStyle(
                  //     //     fontSize: 12,
                  //     //     fontWeight: FontWeight.w400,
                  //     //     color: AppColors.appBlack.withOpacity(0.4)
                  //     // ),
                  //     focusedBorder: OutlineInputBorder(
                  //         borderRadius: BorderRadius.circular(10),
                  //         borderSide: BorderSide.none
                  //
                  //     ),
                  //     enabledBorder: OutlineInputBorder(
                  //
                  //         borderRadius: BorderRadius.circular(10),
                  //         borderSide: BorderSide.none
                  //     ),
                  //
                  //
                  //
                  //   ),
                  //
                  // ),
                ),
                verticalSizedBox(20),
                Row(
                  children: [
                    Expanded(
                      child: CupertinoButton(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 15),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                          color: AppColors.textFieldFill1,
                          child: FittedBox(
                            child: Text("Add",
                                style: AppTextStyle.button2Bold(
                                    textColor: AppColors.appBlack)),
                          ),
                          onPressed: () {
                            Navigator.pop(context);
                            productDetailAndUpdate(true);
                          }),
                    ),
                    // Expanded(
                    //   child: CupertinoButton(
                    //       padding: const EdgeInsets.symmetric(vertical: 10),
                    //       borderRadius: const BorderRadius.all(Radius.circular(30)),
                    //       color: AppColors.inActiveGreen,
                    //       child: Text(
                    //         "",
                    //         style: TextStyle(fontWeight: FontWeight.w600, fontFamily: "LatoSemiBold", fontSize: 15, color: AppColors.brandGreen),
                    //       ),
                    //       onPressed: () {
                    //         Navigator.pop(context);
                    //         productDetailAndUpdate(true);
                    //       }),
                    // ),
                    horizontalSizedBox(20),

                    Expanded(
                      child: CupertinoButton(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 15),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                          color: AppColors.textFieldFill1,
                          child: FittedBox(
                            child: Text("Remove",
                                style: AppTextStyle.button2Bold(
                                    textColor: AppColors.appBlack)),
                          ),
                          onPressed: () {
                            Navigator.pop(context);
                            productDetailAndUpdate(false);
                          }),
                    ),
                    // Expanded(
                    //   child: CupertinoButton(
                    //       padding: EdgeInsets.symmetric(vertical: 10),
                    //       borderRadius: BorderRadius.all(Radius.circular(30)),
                    //       color: AppColors.inActiveGreen,
                    //       child: Text(
                    //         "remove",
                    //         style: TextStyle(fontWeight: FontWeight.w600, fontFamily: "LatoSemiBold", fontSize: 15, color: AppColors.brandGreen),
                    //       ),
                    //       onPressed: () {
                    //         Navigator.pop(context);
                    //         productDetailAndUpdate(false);
                    //       }),
                    // ),
                  ],
                )
              ],
            ),
          );
        });
  }

  //endregion

  //region Get Only Product And Update Api Call
  void productDetailAndUpdate(bool isAdd) async {
    try {
      //Close Keyboard
      //CommonMethods.closeKeyboard(context);
      // editProductCtrl.sink.add(EditProductState.Loading);
      for (int i = 0; i < productReferenceList.length; i++) {
        //Api Call
        getOnlyProductResponse = await productAndImageServices
            .getOnlyProduct(productReferenceList[i]);

        ///Update Stocks
        updateProductStock(isAdd, productReferenceList[i]);
      }
      // editProductCtrl.sink.add(EditProductState.Success);
      CommonMethods.toastMessage(
          "${productReferenceList.length} Product Stocks Updated", context);
      //Clear selected products
      productReferenceList.clear();
      //Change flag
      isMultiSelect = false;
      selectCtrl.sink.add(true);
      //  stockTextCtrl.clear();
      // getProductApiCall();
    } on ApiErrorResponseMessage {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      editProductCtrl.sink.add(EditProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion

  //region Update Stocks
  void updateProductStock(bool addRemove, String productReference) async {
    int stockQuantity;
    //region Check it is add or remove
    ///Add stocks
    if (addRemove) {
      //print(getOnlyProductResponse.singleProductData!.inStock.toString());

      stockQuantity = getOnlyProductResponse.singleProductData!.inStock! +
          int.parse(stockTextCtrl.text);
    }

    ///Remove Stocks
    else {
      stockQuantity = getOnlyProductResponse.singleProductData!.inStock! -
          int.parse(stockTextCtrl.text);
      if (stockQuantity < 0) {
        stockQuantity = 0;
      }
    }
    //endregion

    await editProductAndImageServices.editOnlyProduct(
        productName: getOnlyProductResponse.singleProductData!.productName!,
        brandName: getOnlyProductResponse.singleProductData!.brandName!,
        productCategory:
            getOnlyProductResponse.singleProductData!.productCategory!,
        productDescription:
            getOnlyProductResponse.singleProductData!.productDescription!,
        promotionLink: getOnlyProductResponse.singleProductData!.promotionLink!,
        inStock: stockQuantity,
        mrpPrice: getOnlyProductResponse.singleProductData!.mrpPrice!,
        sellingPrice: getOnlyProductResponse.singleProductData!.sellingPrice!,
        storeid: storeId,
        productReference: productReference,
        swadeshiMade: getOnlyProductResponse.singleProductData!.swadeshiMade!,
        swadeshiBrand: getOnlyProductResponse.singleProductData!.swadeshiBrand!,
        swadeshiOwned: getOnlyProductResponse.singleProductData!.swadeshiOwned!,
        hashTag: getOnlyProductResponse.singleProductData!.hashTag!,
        gender: getOnlyProductResponse.singleProductData!.targetGender!,
        isAffiliatePromotionEnabled:
            getOnlyProductResponse.singleProductData!.isPromotionEnabled!,
        affiliateCommissionAmount:
            getOnlyProductResponse.singleProductData!.promotionAmount!,
            productSlug: getOnlyProductResponse.singleProductData!.productSlug!,
        productCode: getOnlyProductResponse.singleProductData!.productCode!,
        options: getOnlyProductResponse.singleProductData!.options,
    );
  }

//endregion

  //region Go to Product View Screen
  goToViewProductScreen(int index) {
    // var screen =   BuyerViewSingleProductScreen(productReference: "P1031221UXJW",productVersion: "1.0.0",);
    var screen = BuyerViewProductScreen(
      openingFrom: SearchScreenEnum.NON,
      index: index,
      productList: searchProductList,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //If product list are empty then broad cast empty
      if (searchProductList.isEmpty) {
        editProductCtrl.sink.add(EditProductState.Empty);
      }
      //Else success
      else {
        editProductCtrl.sink.add(EditProductState.Success);
      }
      //storeProductsCtrl.sink.add(StoreProductState.Success);
    });
  }

  //endregion

  //region Update Product In List
  void updateProductInList(products.Product updatedProduct) {
    // Update the global ProductDataModel - this will trigger UI rebuilds across the app
    final productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    productDataModel.addProductIntoList(products: [updatedProduct]);

    // Also update the local searchProductList for immediate local updates
    final index = searchProductList.indexWhere(
      (product) => product.productReference == updatedProduct.productReference
    );

    if (index != -1) {
      // Update the product in the local list
      searchProductList[index] = updatedProduct;
    }

    // Refresh the UI to show updated data
    editProductCtrl.sink.add(EditProductState.Success);
  }
  //endregion

//region Dispose
  void dispose() {
    productReferenceList.clear();
  }
//endregion
}

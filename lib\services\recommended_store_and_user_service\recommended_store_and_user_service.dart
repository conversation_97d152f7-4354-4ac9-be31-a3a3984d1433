//happy
import 'package:flutter/cupertino.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class RecommendedStoreAndUserServices {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  RecommendedStoreAndUserServices() {
    httpService = HttpService();
  }

  // endregion

  //region Get recommended store or user
  Future<List<UserAndStoreInfo>> getRecommendedStoreAndUser(
      {required bool isRecommendedStore,
      required int limit,
      required int offset}) async {
    // endregion
    Map<String, dynamic> response;

    // Query
    var query = isRecommendedStore
        ? '''
  query RecommendedStores {
    recommendedStores(reference:"${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}", limit: $limit, offset: $offset) {
        reference
        handle
        name
        icon
        entityType
        subscriptionType
        followersOrSupportersCount
        followingOrSupportingCount
        followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
    }
}
  '''
        : '''
  query RecommendedUsers {
    recommendedUsers(reference:"${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}", limit: $limit, offset: $offset) {
        reference
        handle
        name
        subscriptionType
        icon
        entityType
        followersOrSupportersCount
        followingOrSupportingCount
        followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
    }
}
  ''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);
    // Recommended store or user list
    List<UserAndStoreInfo> storeOrUserList = [];

    //Recommended store and user
    // Check if the response contains the expected data
    // Check if the response contains the expected data
    if (response.containsKey('data') && response['data'] != null) {
      // Determine whether to get recommended stores or users
      List<dynamic> storesJson = response['data']
          [isRecommendedStore ? 'recommendedStores' : 'recommendedUsers'];

      // Map JSON data to RecommendedStoreAndUser objects
      storeOrUserList =
          storesJson.map((json) => UserAndStoreInfo.fromJson(json)).toList();
    } else {
      return storeOrUserList;
      // Handle unexpected response format
      //print('Unexpected response format: $response');
    }

    //print(response);
    return storeOrUserList;
  }
  //endregion

  //region Get recommended products
  Future<List<Product>> getRecommendedProducts(
      {required int limit,
      required int offset,
      required BuildContext context}) async {
    // Get visitor reference (user or store reference based on current view)
    String visitorReference = AppConstants.appData.isUserView!
        ? AppConstants.appData.userReference ?? ""
        : AppConstants.appData.storeReference ?? "";

    // Get user pincode
    String userPincode = AppConstants.appData.pinCode ?? "";

    // Construct the URL for the lean API
    String url =
        "${AppConstants.baseUrl}/lean/recommended_products/?limit=$limit&offset=$offset&visitor_reference=$visitorReference&user_pincode=$userPincode";

    try {
      // Execute the REST API request
      Map<String, dynamic> response = await httpService.getApiCall(url);

      // Initialize empty product list
      List<Product> productList = [];

      // Check if the response contains the expected data
      if (response.containsKey('message') &&
          response['message'] == 'success' &&
          response.containsKey('data') &&
          response['data'] != null) {
        // Get the list of products from the response
        List<dynamic> productsJson = response['data'];

        // Map JSON data to Product objects
        productList = productsJson.map((json) {
          // Map product images
          List<ProdImages> prodImages = [];
          if (json['prod_images'] != null) {
            prodImages = (json['prod_images'] as List).map((imgJson) {
              return ProdImages(
                productimageid: imgJson['productimageid'],
                productImage: imgJson['product_image'],
                reorder: imgJson['reorder'],
              );
            }).toList();
          }

          // Create a Product object with the data from the response
          return Product(
            productid: json['productid'],
            productReference: json['product_reference'],
            productName: json['product_name'] ?? "Product name not available",
            productDescription: json['product_description'] ?? "No description available",
            productCategory: json['product_category'] ?? "Category not available",
            brandName: json['brand_name'],
            // mrpPrice: json['mrp_price'], // taken cared in product variants
            countOfRatings: json['count_of_ratings'] ?? 0,
            rating: json['rating']?.toDouble(),
            // sellingPrice: json['selling_price'], // taken care in product variants
            storeReference: json['store_reference'],
            storeid: json['storeid'],
            storeIcon: json['store_icon'] != null
                ? (json['store_icon'].startsWith('/media/')
                    ? json['store_icon']
                    : "/media/${json['store_icon']}")
                : null,
            storeName: json['store_name'],
            storehandle: json['storehandle'],
            // inStock: json['in_stock'], // taken care in product variants
            hashTag: json['hashtags'],
            prodImages: prodImages,
            likeCount: json['like_count'],
            commentCount: json['comment_count'],
            ordersCount: json['orders_count'],
            returnCount: json['returns_count'],
            createdDate: json['created_date'] ?? DateTime.now().toString(),
            updatedDate: json['modified_date'] ?? DateTime.now().toString(),
            productVersion: json['product_version'] ?? "1.0.0",
            swadeshiMade: json['swadeshi_made'],
            swadeshiBrand: json['swadeshi_brand'],
            swadeshiOwned: json['swadeshi_owned'],
            targetGender: json['targeted_gender'],
            isTestStore: json['is_test_store'],
            subscriptionType: json['subscription_type'],
            openForOrder: json['open_for_order'],
            location: json['location'],
            isDeleted: json['deleted'],
            likeStatus: json['like_status'],
            saveStatus: json['save_status'],
            contentCategory: json['content_category'],
            contentType: json['content_type'],
            contentHeaderText: json['content_header_text'],
            deliverable: json['deliverability'],
            isBuyEnable: json['is_buy_enabled'],
            productStatusMessage: json['product_status_message'],
            configReceiveOrders: json['config_receive_orders'],
            fulfillmentOptions: json['fulfillment_options'],
            deliveryFee: json['delivery_fee'],
            deliveryPartner: json['delivery_partner'],
            logisticPartnerName: json['logistic_partner_name'],
            returnPeriod: json['return_period'] ?? 0,
            returnCostOn: json['return_cost_on'],
            deliveryBy: json['delivery_by'] ?? json['deliveryBy'] ?? 0,
            returnPickupBy: json['return_pickup_by'],
            returnConditions: json['return_conditions'] != null
                ? List<String>.from(json['return_conditions'])
                : [],
            refundResponsibilityList: json['refund_responsibility'] != null
                ? (json['refund_responsibility'] as List)
                    .map((refundJson) => RefundResponsibility(
                          itemHeading: refundJson['item_heading'],
                          itemText: refundJson['item_text'],
                          itemSubtext: refundJson['item_subtext'] ?? '',
                        ))
                    .toList()
                : [],
            disclaimerMessage: json['disclaimer_message'],
            analyticsViewCount: json['analytics_view_count'],
            promotionLink: json['promotion_link'],
            isPromotionEnabled: json['is_promotion_enabled'],
            promotionAmount: json['promotion_amount']?.toDouble(),
            productSlug: json['product_slug'],
            productCode: json['product_code'],
          );
        }).toList();
      }

      return productList;
    } catch (e) {
      // Handle any errors and return an empty list
      // Error is silently handled and an empty list is returned
      return [];
    }
  }
//endregion
}

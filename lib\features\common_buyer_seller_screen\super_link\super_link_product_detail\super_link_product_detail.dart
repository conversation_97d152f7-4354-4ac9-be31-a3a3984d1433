import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_label_dropdown/product_label_dropdown.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:url_launcher/url_launcher.dart';

class SuperLinkProductDetail extends StatefulWidget {
  final Product product;

  const SuperLinkProductDetail({super.key, required this.product});

  @override
  State<SuperLinkProductDetail> createState() => _SuperLinkProductDetailState();
}

class _SuperLinkProductDetailState extends State<SuperLinkProductDetail> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      child: Column(
        children: [
          Hero(
            tag: widget.product.productReference!,
            child: Container(
              margin: const EdgeInsets.only(bottom: 32),
              height: CommonMethods.calculateWebWidth(context: context) * 0.7,
              width: CommonMethods.calculateWebWidth(context: context) * 0.7,
              child: ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(CommonMethods.calculateWebWidth(context: context) * 0.06)),

                child: widget.product.prodImages == null || widget.product.prodImages!.isEmpty
                    ? SvgPicture.asset(
                        AppImages.productPlaceHolder,
                        fit: BoxFit.fill,
                      )
                    : extendedImage(widget.product.prodImages!.first.productImage, context, 1000, 1000,
                        customPlaceHolder: AppImages.productPlaceHolder,
                        fit: BoxFit.cover,
                        imageHeight: CommonMethods.calculateWebWidth(context: context) * 0.7,
                        imageWidth: CommonMethods.calculateWebWidth(context: context) * 0.7),
              ),
            ),
          ),
          brandAndName(),
          priceInfo(),
          Container(
              margin: const EdgeInsets.only(top: 5),
              child: ProductLabelDropdown(product: widget.product,padding: 0,)),
          buttons(),
        ],
      ),
    );
  }
//endregion


//region Brand and Name
Widget brandAndName(){
    return Container(
      alignment: Alignment.centerLeft,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text:widget.product.brandName,
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack).copyWith(fontWeight: FontWeight.w900),
            ),
            TextSpan(
              text:" ${widget.product.productName}",
              style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),
            ),

          ],
        ),
      ),
    );
}
//endregion

//region Price info
Widget priceInfo(){
    final pricing = AppCommonWidgets.getDisplayPricingFromProduct(widget.product);
    final sellingPrice = pricing['sellingPrice']!;
    final mrpPrice = pricing['mrpPrice']!;

    return Container(
      margin: const EdgeInsets.only(top: 5),
      child: Row(
        children: [
          Text("₹ $sellingPrice", style: AppTextStyle.access0(textColor: AppColors.appBlack).copyWith(fontWeight: FontWeight.w900)),
          const SizedBox(width: 10,),
          Text("₹ $mrpPrice",
              style: AppTextStyle.access0(textColor: AppColors.writingBlack1, isLineThrough: true).copyWith(fontWeight: FontWeight.w400)),
        ],
      ),
    );

}
//endregion


//region Buttons
Widget buttons(){
    return Column(
      children: [
        const SizedBox(height: 12,),
        CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () {

            //if web then lunch url
            if(kIsWeb){
              launch(AppLinkCreateService().createProductLink(productReference: widget.product.productReference!));
                  //AppLinkCreateService().createProductLink(productReference: product.productReference!)
            }
            else{
              Navigator.pop(context);
              launch(AppLinkCreateService().createProductLink(productReference: widget.product.productReference!));
            }
          },
          child: Container(
            alignment: Alignment.center,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 10),
            decoration: BoxDecoration(
              color: AppColors.writingBlack2,
              borderRadius:BorderRadius.circular(100),
            ),
            child: Text("Buy Now Directly",style: AppTextStyle.access0(textColor: AppColors.appWhite).copyWith(fontWeight: FontWeight.w600),),
          ),
        ),
        const SizedBox(height: 20,),
        CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            //if web then lunch url
            if(kIsWeb){
              launch(AppLinkCreateService().createProductLink(productReference: widget.product.productReference!));
              //AppLinkCreateService().createProductLink(productReference: product.productReference!)
            }
            else{
              Navigator.pop(context);
              launch(AppLinkCreateService().createProductLink(productReference: widget.product.productReference!));
            }
          },
          child: Container(
            alignment: Alignment.center,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 10),
            decoration: BoxDecoration(
              borderRadius:BorderRadius.circular(100),
              border: Border.all(color: AppColors.appBlack, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("View more details",style: AppTextStyle.access0(textColor: AppColors.writingBlack2).copyWith(fontWeight: FontWeight.w600),),
                Container(
                    height: 23, width: 24,
                    child: Image.asset(AppImages.linkOpenArrow, height: 23, width: 24,color: AppColors.appBlack,)),
              ],
            ),
          ),
        )
      ],
    );
}
//endregion

}

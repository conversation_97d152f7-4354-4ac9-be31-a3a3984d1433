import 'package:flutter/material.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

/// A reusable widget component that displays variant information
/// Similar to how it's shown in product detail full card
class VariantDisplayWidget extends StatelessWidget {
  final ProductVariant? variantDetails;
  final bool showLabel;
  final bool isCompact;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;

  const VariantDisplayWidget({
    Key? key,
    this.variantDetails,
    this.showLabel = true,
    this.isCompact = false,
    this.labelStyle,
    this.valueStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // If no variant details, return empty widget
    if (variantDetails == null || variantDetails!.combinations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showLabel && !isCompact) ...[
          Text(
            "Selected: ",
            style: labelStyle ?? AppTextStyle.smallText(
              textColor: AppColors.appBlack,
            ),
          ),
          const SizedBox(height: 4),
        ],
        _buildVariantCombinations(),
      ],
    );
  }

  Widget _buildVariantCombinations() {
    if (variantDetails!.combinations.isEmpty) {
      return Text(
        "Standard",
        style: valueStyle ?? AppTextStyle.smallTextRegular(
          textColor: AppColors.appBlack,
        ),
      );
    }

    if (isCompact) {
      // Compact display for order cards
      return Wrap(
        spacing: 8,
        runSpacing: 4,
        children: variantDetails!.combinations.entries.map((entry) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.appBlack.withOpacity(0.1)),
            ),
            child: Text(
              "${entry.key}: ${entry.value}",
              style: valueStyle ?? AppTextStyle.smallText(
                textColor: AppColors.appBlack,
              ),
            ),
          );
        }).toList(),
      );
    } else {
      // Full display similar to product detail full card
      return Wrap(
        spacing: 10,
        runSpacing: 4,
        children: variantDetails!.combinations.entries.map((entry) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${entry.key}: ",
                style: labelStyle ?? AppTextStyle.smallTextRegular(
                  textColor: AppColors.writingBlack1,
                ),
              ),
              Text(
                entry.value,
                style: valueStyle ?? AppTextStyle.smallText(
                  textColor: AppColors.appBlack,
                ),
              ),
            ],
          );
        }).toList(),
      );
    }
  }
}

/// A variant display widget specifically designed for order items
class OrderVariantDisplayWidget extends StatelessWidget {
  final ProductVariant? variantDetails;
  final bool showPriceInfo;

  const OrderVariantDisplayWidget({
    Key? key,
    this.variantDetails,
    this.showPriceInfo = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (variantDetails == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Variant combinations
        if (variantDetails!.combinations.isNotEmpty)
          VariantDisplayWidget(
            variantDetails: variantDetails,
            showLabel: false,
            isCompact: true,
            valueStyle: AppTextStyle.smallText(
              textColor: AppColors.writingBlack1,
            ),
          ),
        
        // Price information (if requested)
        if (showPriceInfo) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                "₹${variantDetails!.sellingPrice}",
                style: AppTextStyle.smallText(
                  textColor: AppColors.appBlack,
                ),
              ),
              if (variantDetails!.mrpPrice != variantDetails!.sellingPrice) ...[
                const SizedBox(width: 8),
                Text(
                  "₹${variantDetails!.mrpPrice}",
                  style: AppTextStyle.smallText(
                    textColor: AppColors.writingBlack1,
                    isLineThrough: true,
                  ),
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }
}

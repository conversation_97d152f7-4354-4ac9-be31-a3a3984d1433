import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/inventory_options/add_option_bottom_sheet.dart';
import 'package:swadesic/features/seller/inventory_options/add_variant_bottom_sheet.dart';
import 'package:swadesic/model/product_option/product_option.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/product_variant_service/product_variant_service.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/app_strings.dart';

enum InventoryOptionsState { Loading, Success, Failed, Empty }

class InventoryOptionsBloc {
  BuildContext context;
  final String storeReference;
  final Product product;

  // State variables
  bool hasOptions = false;
  List<ProductOption> productOptions = [];
  List<ProductVariant> productVariants = [];

  // Temporary storage for unsaved changes
  bool tempHasOptions = false;
  List<ProductOption> tempProductOptions = [];
  List<ProductVariant> tempProductVariants = [];

  // Cache for preserving data when toggling between Yes/No
  List<ProductOption> cachedProductOptions = [];
  List<ProductVariant> cachedProductVariants = [];

  // Controllers for no-options scenario
  late TextEditingController stockController;
  late TextEditingController mrpController;
  late TextEditingController sellingPriceController;

  // Services
  late ProductVariantService productVariantService;

  // Controllers
  final refreshCtrl = StreamController<bool>.broadcast();
  final stateCtrl = StreamController<InventoryOptionsState>.broadcast();

  InventoryOptionsBloc(this.context, this.storeReference, this.product) {
    productVariantService = ProductVariantService();

    // Initialize controllers
    stockController = TextEditingController();
    mrpController = TextEditingController();
    sellingPriceController = TextEditingController();

    // Add listeners to update UI when text changes
    stockController.addListener(() => refreshCtrl.sink.add(true));
    mrpController.addListener(() => refreshCtrl.sink.add(true));
    sellingPriceController.addListener(() => refreshCtrl.sink.add(true));
  }

  void init() {
    // Initialize with existing product options if available
    if (product.options != null && product.options!.isNotEmpty) {
      hasOptions = true;
      productOptions = product.options!.entries
          .map((entry) => ProductOption(
                optionName: entry.key,
                optionValues: entry.value,
              ))
          .toList();
    }

    // Load cached variants from product if available
    if (product.variants != null && product.variants!.isNotEmpty) {
      // Check if we have variants with combinations (indicating options exist)
      final variantsWithCombinations = product.variants!
          .map((variantJson) => ProductVariant.fromJson(variantJson))
          .where((variant) => variant.combinations.isNotEmpty)
          .toList();

      if (variantsWithCombinations.isNotEmpty) {
        // Product has variants with combinations, so it has options
        hasOptions = true;
        productVariants = variantsWithCombinations;

        // If options weren't already loaded from product.options, extract them from variants
        if (productOptions.isEmpty) {
          final optionsMap = <String, Set<String>>{};
          for (final variant in variantsWithCombinations) {
            variant.combinations.forEach((key, value) {
              optionsMap.putIfAbsent(key, () => <String>{}).add(value);
            });
          }
          productOptions = optionsMap.entries
              .map((entry) => ProductOption(
                    optionName: entry.key,
                    optionValues: entry.value.toList(),
                  ))
              .toList();
        }
      } else {
        // Check if we have a no-variant data (product without options but with variant data)
        final noVariantList = product.variants!
            .map((variantJson) => ProductVariant.fromJson(variantJson))
            .where((variant) => variant.combinations.isEmpty)
            .toList();

        if (noVariantList.isNotEmpty) {
          // Load the no-variant data into controllers
          final noVariantData = noVariantList.first;
          stockController.text = noVariantData.stock.toString();
          mrpController.text = noVariantData.mrpPrice.toString();
          sellingPriceController.text = noVariantData.sellingPrice.toString();
        }
      }
    }

    // Only load from API if this is a real product (not a temporary one from edit form)
    // and we don't already have cached data
    if (product.productReference != null &&
        product.productReference != "New" &&
        product.productReference != "cached_data" &&
        productVariants.isEmpty &&
        !hasOptions) {
      loadProductVariants();
    }

    // Initialize controllers for no-options scenario if not already set
    if (stockController.text.isEmpty) {
      stockController.text = product.inStock?.toString() ?? '';
    }
    if (mrpController.text.isEmpty) {
      mrpController.text = product.mrpPrice?.toString() ?? '';
    }
    if (sellingPriceController.text.isEmpty) {
      sellingPriceController.text = product.sellingPrice?.toString() ?? '';
    }

    // Initialize temp variables with current state
    tempHasOptions = hasOptions;
    tempProductOptions = List.from(productOptions);
    tempProductVariants = List.from(productVariants);

    // Initialize cache with the loaded data to preserve it during toggle
    cachedProductOptions = List.from(productOptions);
    cachedProductVariants = List.from(productVariants);

    refreshCtrl.sink.add(true);
  }

  void setHasOptions(bool value) {
    tempHasOptions = value;
    if (!tempHasOptions) {
      // When switching to "No", update cache with current temp data before clearing
      if (tempProductOptions.isNotEmpty || tempProductVariants.isNotEmpty) {
        cachedProductOptions = List.from(tempProductOptions);
        cachedProductVariants = List.from(tempProductVariants);
      }
      // Clear temp data for "No" option
      tempProductOptions.clear();
      tempProductVariants.clear();
    } else {
      // When switching to "Yes", restore from cache if available
      if (cachedProductOptions.isNotEmpty || cachedProductVariants.isNotEmpty) {
        tempProductOptions = List.from(cachedProductOptions);
        tempProductVariants = List.from(cachedProductVariants);
      }
    }
    refreshCtrl.sink.add(true);
  }

  void showAddOptionBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddOptionBottomSheet(
        onOptionAdded: (option) {
          addProductOption(option);
        },
      ),
    );
  }

  void showEditOptionBottomSheet(ProductOption option) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddOptionBottomSheet(
        existingOption: option,
        onOptionAdded: (updatedOption) {
          updateProductOption(option, updatedOption);
        },
        onOptionDeleted: () {
          deleteProductOption(option);
        },
      ),
    );
  }

  void showAddVariantBottomSheet() {
    if (tempProductOptions.isEmpty) {
      CommonMethods.toastMessage(
        AppStrings.pleaseAddProductOptionsFirst,
        context,
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVariantBottomSheet(
        productOptions: tempProductOptions,
        existingVariants: tempProductVariants,
        onVariantAdded: (variant) {
          addProductVariant(variant);
        },
      ),
    );
  }

  void showEditVariantBottomSheet(ProductVariant variant) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVariantBottomSheet(
        productOptions: tempProductOptions,
        existingVariants: tempProductVariants,
        existingVariant: variant,
        onVariantAdded: (updatedVariant) {
          updateProductVariant(variant, updatedVariant);
        },
        onVariantDeleted: () {
          deleteProductVariant(variant);
        },
      ),
    );
  }

  void addProductOption(ProductOption option) {
    // Check if option name already exists
    if (tempProductOptions.any((existing) =>
        existing.optionName.toLowerCase() == option.optionName.toLowerCase())) {
      CommonMethods.toastMessage(
        AppStrings.optionWithThisNameAlreadyExists,
        context,
      );
      return;
    }

    tempProductOptions.add(option);
    // Update cache to preserve new data
    cachedProductOptions = List.from(tempProductOptions);
    refreshCtrl.sink.add(true);
  }

  void updateProductOption(ProductOption oldOption, ProductOption newOption) {
    final index = tempProductOptions.indexOf(oldOption);
    if (index != -1) {
      tempProductOptions[index] = newOption;

      // Update variants that use this option
      for (var variant in tempProductVariants) {
        if (variant.combinations.containsKey(oldOption.optionName)) {
          final value = variant.combinations[oldOption.optionName];
          variant.combinations.remove(oldOption.optionName);
          if (newOption.optionValues.contains(value)) {
            variant.combinations[newOption.optionName] = value!;
          }
        }
      }

      // Update cache to preserve changes
      cachedProductOptions = List.from(tempProductOptions);
      cachedProductVariants = List.from(tempProductVariants);
      refreshCtrl.sink.add(true);
    }
  }

  void deleteProductOption(ProductOption option) {
    tempProductOptions.remove(option);

    // Remove variants that use this option
    tempProductVariants.removeWhere((variant) =>
        variant.combinations.containsKey(option.optionName));

    // Update cache to preserve changes
    cachedProductOptions = List.from(tempProductOptions);
    cachedProductVariants = List.from(tempProductVariants);
    refreshCtrl.sink.add(true);
  }

  void addProductVariant(ProductVariant variant) {
    // Check if variant with same combinations already exists
    if (tempProductVariants.any((existing) =>
        existing.hasSameCombinations(variant.combinations))) {
      CommonMethods.toastMessage(
        AppStrings.thisVariantAlreadyExists,
        context,
      );
      return;
    }

    tempProductVariants.add(variant);
    // Update cache to preserve new data
    cachedProductVariants = List.from(tempProductVariants);
    refreshCtrl.sink.add(true);
  }

  void updateProductVariant(ProductVariant oldVariant, ProductVariant newVariant) {
    final index = tempProductVariants.indexOf(oldVariant);
    if (index != -1) {
      tempProductVariants[index] = newVariant;
      // Update cache to preserve changes
      cachedProductVariants = List.from(tempProductVariants);
      refreshCtrl.sink.add(true);
    }
  }

  void deleteProductVariant(ProductVariant variant) {
    tempProductVariants.remove(variant);
    // Update cache to preserve changes
    cachedProductVariants = List.from(tempProductVariants);
    refreshCtrl.sink.add(true);
  }

  Future<void> loadProductVariants() async {
    try {
      stateCtrl.sink.add(InventoryOptionsState.Loading);
      final response = await productVariantService.getProductVariants(
        productReference: product.productReference!,
      );
      productVariants = response.productVariants;
      stateCtrl.sink.add(InventoryOptionsState.Success);
      refreshCtrl.sink.add(true);
    } catch (e) {
      stateCtrl.sink.add(InventoryOptionsState.Failed);
      CommonMethods.toastMessage(
        "${AppStrings.failedToLoadVariants}: ${e.toString()}",
        context,
      );
    }
  }

  void onTapSave() {
    // Commit temporary changes to actual data
    hasOptions = tempHasOptions;
    productOptions = List.from(tempProductOptions);
    productVariants = List.from(tempProductVariants);

    // Update cache with saved data
    cachedProductOptions = List.from(productOptions);
    cachedProductVariants = List.from(productVariants);

    if (hasOptions && productOptions.isNotEmpty) {
      // Has options scenario
      product.options = {};
      for (var option in productOptions) {
        product.options![option.optionName] = option.optionValues;
      }
      // Cache variants in product for persistence
      product.variants = productVariants.map((variant) => variant.toJson()).toList();
    } else {
      // No options scenario - create a single variant with empty combinations
      product.options = {};

      // Update product with basic pricing info
      product.inStock = int.tryParse(stockController.text) ?? 0;
      product.mrpPrice = int.tryParse(mrpController.text) ?? 0;
      product.sellingPrice = int.tryParse(sellingPriceController.text) ?? 0;

      // Create a single variant with empty combinations for API consistency
      final singleVariant = ProductVariant(
        combinations: {}, // Empty combinations for no-options scenario
        mrpPrice: product.mrpPrice ?? 0,
        sellingPrice: product.sellingPrice ?? 0,
        stock: product.inStock ?? 0,
        productReference: product.productReference,
      );

      product.variants = [singleVariant.toJson()];
    }

    // Close screen and return data in the format expected by AddEditProductFieldsBloc
    final resultData = {
      'options': product.options ?? {},
      'variants': productVariants,
      'hasMultipleOptions': hasOptions && productOptions.isNotEmpty,
    };

    Navigator.pop(context, resultData);
  }

  void dispose() {
    refreshCtrl.close();
    stateCtrl.close();
    stockController.dispose();
    mrpController.dispose();
    sellingPriceController.dispose();
  }
}

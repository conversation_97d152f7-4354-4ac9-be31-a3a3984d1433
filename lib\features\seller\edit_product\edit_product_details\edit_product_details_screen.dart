import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields.dart';
import 'package:swadesic/features/seller/add_product/add_product_common_widgets.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_details/edit_product_details_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/seller/inventory_options/inventory_options_screen.dart';

// region Edit Product Details
class EditProductDetailsScreen extends StatefulWidget {
  final int storeId;
  final List<String> productReferenceList;
  const EditProductDetailsScreen(
      {Key? key, required this.storeId, required this.productReferenceList})
      : super(key: key);

  @override
  _EditProductDetailsScreenState createState() =>
      _EditProductDetailsScreenState();
}
// endregion

class _EditProductDetailsScreenState extends State<EditProductDetailsScreen> {
  // region Bloc
  late EditProductDetailsBloc editProductDetailsBloc;

  // endregion

  // region Init
  @override
  void initState() {
    editProductDetailsBloc = EditProductDetailsBloc(
        context, widget.storeId, widget.productReferenceList);
    editProductDetailsBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        appBar: appBar(),
        backgroundColor: AppColors.appWhite,
        body: SafeArea(child: body()),
      ),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: true,
        customTitleWidget: StreamBuilder<int>(
            stream: editProductDetailsBloc.editProductNumberCtrl.stream,
            initialData: editProductDetailsBloc.i,
            builder: (context, snapshot) {
              return snapshot.data! + 1 ==
                      editProductDetailsBloc.productReferenceList.length
                  ? AppCommonWidgets.appBarTitleText(
                      text: AppStrings.editProduct)
                  : AppCommonWidgets.appBarTitleText(
                      text:
                          "${AppStrings.editProduct} (${snapshot.data! + 1}/${editProductDetailsBloc.productReferenceList.length})");
            }),
        // titleWidget: Text(UserProfileBloc.getUserDetailsResponse.userDetail!.userName!??"", style: TextStyle(fontFamily: AppConstants.rRegular, fontSize: 19, fontWeight: FontWeight.w700, color: AppColors.appBlack)),
        isDefaultMenuVisible: false,
        isDropdownVisible: false,
        isCartVisible: false,
        isMembershipVisible: false,
        isTextButtonVisible: true,
        textButtonWidget: AppCommonWidgets.appBarTextButtonText(
            text: editProductDetailsBloc.productReferenceList.length == 1
                ? "Done"
                : AppStrings.next),
        onTapTextButton: () {
          editProductDetailsBloc.putEditApiCall();
        });
  }

  //endregion

  // region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        children: [
          verticalSizedBox(38),
          addImage(),
          StreamBuilder<EditProductDetailsState>(
              stream: editProductDetailsBloc.editProductDetailCtrl.stream,
              initialData: EditProductDetailsState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == EditProductDetailsState.Success) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          //brandName(),
                          verticalSizedBox(34),
                          addEditProductTextFields(),
                          inventoryOptions(),
                          addLabels(),
                          verticalSizedBox(24),
                          deliverySetting(),
                          verticalSizedBox(24),
                          returnPolicy(),
                          AppCommonWidgets.bottomListSpace(context: context)
                        ],
                      ),
                    ),
                  );
                }
                if (snapshot.data == EditProductDetailsState.Loading) {
                  return Center(
                    child: AppCommonWidgets.appCircularProgress(),
                  );
                }
                return Center(
                  child: AppCommonWidgets.errorMessage(
                      error: AppStrings.commonErrorMessage),
                );
              }),
        ],
      ),
    );
  }

  // endregion

    //region Inventory Options
  Widget inventoryOptions() {
    return AddProductCommonWidgets.deliveryReturnButton(
      buttonName: AppStrings.inventoryOptions,
      onPress: () {
        editProductDetailsBloc.goToInventoryOptions();
      },
      context: context,
      isDoneVisible: (editProductDetailsBloc.getOnlyProductResponse.singleProductData!.options != null && editProductDetailsBloc.getOnlyProductResponse.singleProductData!.options!.isNotEmpty) ||
          (editProductDetailsBloc.getOnlyProductResponse.singleProductData!.variants != null && editProductDetailsBloc.getOnlyProductResponse.singleProductData!.variants!.isNotEmpty),
    );
  }
  //endregion

  //region Add Images
  Widget addImage() {
    return StreamBuilder<EditProductImageState>(
        stream: editProductDetailsBloc.editProductImageCtrl.stream,
        builder: (context, snapshot) {
          if (snapshot.data == EditProductImageState.Success) {
            return Align(
              alignment: Alignment.center,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  StreamBuilder<bool>(
                      stream: editProductDetailsBloc.imageCtrl.stream,
                      initialData: false,
                      builder: (context, snapshot) {
                        if (editProductDetailsBloc.productImageResponse.data !=
                            null) {
                          return InkWell(
                            onTap: () {
                              editProductDetailsBloc
                                  .goToSelectedImageEditPreviewScreen();
                            },
                            child: CustomImageContainer(
                              width: 75,
                              height: 75,
                              imageUrl: editProductDetailsBloc
                                      .productImageResponse.data!.isEmpty
                                  ? null
                                  : editProductDetailsBloc.productImageResponse
                                      .data![0].productImage,
                              imageType: CustomImageContainerType.product,
                            ),

                            // Container(
                            //   height:75,
                            //   width: 75,
                            //   decoration: const BoxDecoration(
                            //       color: AppColors.appWhite,
                            //       borderRadius: BorderRadius.all(Radius.circular(11))
                            //   ),
                            //   child: ClipRRect(
                            //       borderRadius: const BorderRadius.all(Radius.circular(11)),
                            //       child:   editProductDetailsBloc.productImageResponse.data!.isEmpty?SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.fill,):
                            //       editProductDetailsBloc.productImageResponse.data![0].productImage==null?
                            //
                            //       SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.fill,)
                            //           :extendedImage(editProductDetailsBloc.productImageResponse.data![0].productImage.toString(),
                            //         customPlaceHolder: AppImages.productPlaceHolder,
                            //         context,200,200,cache: true, )
                            //   ),
                            // ),
                          );
                        }
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(CommonMethods()
                              .getBorderRadius(
                                  height: 75,
                                  imageType: CustomImageContainerType.product)),
                          child: Container(
                            height: 75,
                            width: 75,
                            decoration: const BoxDecoration(
                                color: AppColors.lightGray2,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(11))),
                          ),
                        );
                      }),
                  verticalSizedBox(24),
                  Consumer<AppConfigDataModel>(
                    builder: (BuildContext context, AppConfigDataModel value,
                        Widget? child) {
                      return CupertinoButton(
                          alignment: Alignment.center,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(9)),
                          color: AppColors.brandBlack,
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(AppImages.plus,
                                  color: AppColors.appWhite,
                                  height: 16,
                                  width: 16),
                              horizontalSizedBox(10),
                              Text(
                                "${AppStrings.addImages} (up to ${value.appConfig!.productImageLimit})",
                                style: AppTextStyle.contentText0(
                                    textColor: AppColors.appWhite),
                              )
                            ],
                          ),
                          onPressed: () {
                            editProductDetailsBloc
                                .goToSelectedImageEditPreviewScreen();
                          });
                    },
                  )
                ],
              ),
            );
          }
          if (snapshot.data == EditProductImageState.Loading) {
            return Center(
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          return SvgPicture.asset(
            AppImages.productPlaceHolder,
            height: 75,
            width: 75,
          );
        });
  }

  //endregion

// Add edit product Text fields
  Widget addEditProductTextFields() {
    return const AddEditProductFields();
  }
  //endregion

  //region Delivery Setting
  // Widget deliverySetting(){
  //   return EditProductCommonWidgets.greenButton(buttonName: AppStrings.setDeliverySettings, onPress: (){
  //     editProductDetailsBloc.goToDeliverSettingScreen();
  //
  //   });
  // }
  //endregion

  //region Return Policy
  // Widget returnPolicy(){
  //   return EditProductCommonWidgets.greenButton(buttonName: AppStrings.setReturnPolicy, onPress: (){
  //     editProductDetailsBloc.goToSellerReturnProductWarranty();
  //     //addProductBloc.goToReturnPolicy();
  //   });
  // }
//endregion

  //region Add labels
  Widget addLabels() {
    return AddProductCommonWidgets.deliveryReturnButton(
      buttonName: AppStrings.addSwadesicLabels,
      onPress: () {
        editProductDetailsBloc.goToLabels(
            storeReference: editProductDetailsBloc
                .getOnlyProductResponse.singleProductData!.storeReference!);
      },
      context: context,
      isDoneVisible: editProductDetailsBloc
                  .getOnlyProductResponse.singleProductData!.swadeshiBrand !=
              null &&
          editProductDetailsBloc
                  .getOnlyProductResponse.singleProductData!.swadeshiMade !=
              null,
    );
  }
  //endregion

  //region Delivery Setting
  Widget deliverySetting() {
    return AddProductCommonWidgets.deliveryReturnButton(
        buttonName: AppStrings.setDeliverySettings,
        onPress: () {
          editProductDetailsBloc.goToDeliverSettingScreen();
        },
        context: context,
        isDoneVisible: true);
  }
  //endregion

  //region Return Policy
  Widget returnPolicy() {
    return AddProductCommonWidgets.deliveryReturnButton(
        buttonName: AppStrings.setReturnSettings,
        onPress: () {
          editProductDetailsBloc.goToSellerReturnProductWarranty();
        },
        context: context,
        isDoneVisible: true);
  }
//endregion


  // void goToInventoryOptions({required String storeReference}) async {
  //   var screen = InventoryOptionsScreen(
  //     storeReference: storeReference,
  //     product: editProductDetailsBloc.getOnlyProductResponse.singleProductData!,
  //   );
  //   var route = MaterialPageRoute(builder: (context) => screen);
  //   Navigator.push(context, route).then((value) {
  //     if (value != null) {
  //       //If it has data
  //       if (value != null) {
  //         CommonMethods.toastMessage(
  //             AppStrings.inventoryOptionsUpdated, context,
  //             toastShowTimer: 3);
  //         //Refresh options
  //         optionsRefreshCtrl.sink.add(true);
  //       }
  //     } else {
  //       return;
  //     }
  //   });
  // }
}

import 'dart:async';

import 'package:flip_card/flip_card.dart';
import 'package:flip_card/flip_card_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_product_detail/buyer_product_details_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_full_view/product_full_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/external_reviews/create_external_review_request_screen.dart';
import 'package:swadesic/features/post/liked_user_or_stores/liked_user_or_store_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_details/edit_product_details_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/services/add_visited_reference/add_visited_references.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/app_link_services/page_url_service.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class ProductDetailFullCardBloc {
  // region Common Variables
  BuildContext context;

  final bool isFromAddProduct;
  final Product product;
  PageController pageController = PageController();
  final FlipCardController flipController = FlipCardController();

  // Variant-related properties
  ProductVariant? selectedVariant;
  List<ProductVariant> availableVariants = [];

  // Static scroll controller for reviews section
  static final ScrollController reviewsScrollController = ScrollController();

  // Static method to scroll to reviews section
  static void scrollToReviews() {
    if (reviewsScrollController.hasClients) {
      reviewsScrollController.animateTo(
        reviewsScrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  //endregion

  //region Controller
  final sliderCtrl = StreamController<int>.broadcast();
  final switchProductImageAndStoryIconCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  ProductDetailFullCardBloc(this.context, this.product, this.isFromAddProduct);
// endregion

//region init
  void init() {
    initializeVariants();
  }
//endregion

  //region On tap drawer
  void onTapDrawer(
      {required String productReference,
      required String storeReference,
      required String? productImage}) async {
    List<Map<String, dynamic>> accessOptions = [
      {
        'title': AppStrings.copyProductLink,
        'onTap': () {
          Navigator.pop(context);
          CommonMethods.copyText(
              context,
              AppLinkCreateService()
                  .createProductLink(productReference: productReference));
        },
      },
      {
        'title': AppStrings.shareTheProduct,
        'onTap': () {
          Navigator.pop(context);
          this.onTapShare(
              imageUrl: productImage, productReference: productReference);
        },
      },
      {
        'title': AppStrings.reportTheProduct,
        'onTap': () {
          Navigator.pop(context);
          // Navigator.pop(context);

          //If non register user
          if (CommonMethods().isStaticUser()) {
            CommonMethods().goToSignUpFlow();
            return;
          }

          var screen = ReportScreen(
            reference: productReference,
            isProduct: true,
          );
          var route = MaterialPageRoute(builder: (context) => screen);
          Navigator.push(context, route);

//endregion
        },
      },
      // Add more options if needed
    ];

    //If store owner view product then show edit product option
    if (AppConstants.appData.storeReference != null &&
        product.storeReference == AppConstants.appData.storeReference) {
      accessOptions.add({
        'title': AppStrings.editProduct,
        'onTap': () {
          Navigator.pop(context);
          onTapEditDetails(
              productReference: product.productReference!,
              storeId: product.storeid!);
        },
      });

      accessOptions.add({
        'title': 'Create External Product Review Link',
        'onTap': () {
          Navigator.pop(context);
          onTapCreateExternalReviewLink(
              productReference: productReference, productImage: productImage);
        },
      });

      accessOptions.add({
        'title': AppStrings.deleteProduct,
        'onTap': () {
          askConfirmBeforeDelete(productReference: productReference);
        },
      });
    }

    CommonMethods.accessBottomSheet(
      screen: ShareAccessBottomSheet(accessOptions: accessOptions),
      context: context,
    );
  }
//endregion

  //region Go to store screen
  void goToStore({required String storeReference}) {
    var screen = BuyerViewStoreScreen(storeReference: storeReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to Buyer Product Detail Screen
  void goToProductDetail({required Product selectedProduct}) {
    var screen = BuyerProductDetailsScreen(
        isFromAddProduct: isFromAddProduct, product: selectedProduct);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to Buyer Image Preview Screen
  void goToBuyerProductImageScreen(
      {required List<ProdImages> productImage, required imageIndex}) {
    List<String> imageUrls = [];
    for (var data in productImage) {
      imageUrls.add(data.productImage!);
    }
    var screen = BuyerImagePreviewScreen(
      productImage: imageUrls,
      imageIndex: imageIndex,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }
//endregion

  //region On tap edit details
  void onTapEditDetails(
      {required String productReference, required int storeId}) {
    //Add product reference
    var screen = EditProductDetailsScreen(
      storeId: storeId,
      productReferenceList: [productReference],
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // If we got updated product data back, update the current product
      if (value != null && value is Product) {
        // Update the product data with the returned updated data
        // Note: Since product is final, we need to update its properties
        updateProductData(value);
      }
    });
  }

  //region Update Product Data
  void updateProductData(Product updatedProduct) {
    // Update the product properties with the new data
    product.productName = updatedProduct.productName;
    product.brandName = updatedProduct.brandName;
    product.productDescription = updatedProduct.productDescription;
    product.productCategory = updatedProduct.productCategory;
    // product.mrpPrice = updatedProduct.mrpPrice;
    // product.sellingPrice = updatedProduct.sellingPrice;
    // product.inStock = updatedProduct.inStock;
    product.promotionLink = updatedProduct.promotionLink;
    product.options = updatedProduct.options;
    product.variants = updatedProduct.variants;
    product.prodImages = updatedProduct.prodImages;

    // Update variant-related data if needed
    if (updatedProduct.variants != null && updatedProduct.variants!.isNotEmpty) {
      availableVariants = updatedProduct.variants!
          .map((variantJson) => ProductVariant.fromJson(variantJson))
          .toList();

      // Update selected variant if it still exists
      if (selectedVariant != null) {
        final updatedSelectedVariant = availableVariants.firstWhere(
          (variant) => variant.variantReference == selectedVariant!.variantReference,
          orElse: () => availableVariants.first,
        );
        selectedVariant = updatedSelectedVariant;
      }
    }

    // Trigger UI refresh if there's a stream controller for it
    // Note: This depends on how the UI is structured in the specific implementation
  }
  //endregion
//endregion

  //region Go to liked user or store
  void goToLikedUsedOrStoreScreen({required String reference}) {
    var screen = LikedUserOrStoreScreen(contentReference: reference);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region On tap heart
  Future<void> onTapHeart(
      {required Product product, bool isDoubleTapped = false}) async {
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    //If static user then go to onboarding
    if (CommonMethods().isStaticUser()) {
      return CommonMethods().goToSignUpFlow();
    }
    try {
      //If already liked and double tapped then return
      if (product.likeStatus! && isDoubleTapped) {
        return;
      }
      //Update like status in product data model
      productDataModel.likeStatusUpdate(
          productReference: product.productReference!,
          likeStatus: !product.likeStatus!);

      //Api call
      bool status = await PostService().likePost(
          postReference: product.productReference!,
          likeStatus: isDoubleTapped ? true : product.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      return;
    }
  }
//endregion

  //region View comment
  viewComment({required String productRef}) {
    var screen = SinglePostViewScreen(
      postReference: productRef,
      isFromProductScreen: true,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region On Tap Share
  void onTapShare(
      {required String? imageUrl, required String productReference}) {
    print('DEBUG: Sharing product with details:');
    print('DEBUG: Store Name: ${product.storeName}');
    print('DEBUG: Product Name: ${product.productName}');
    print('DEBUG: Brand Name: ${product.brandName}');
    print('DEBUG: Store Icon: ${product.storeIcon}');

    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        imageType: CustomImageContainerType.product,
        entityType: EntityType.PRODUCT,
        message:
            "${(AppConstants.appData.isStoreView! && AppConstants.appData.storeReference == product.storeReference) ? AppStrings.ownerSharingAProductMessage : AppStrings.otherSharingAProductMessage}",
        url: AppLinkCreateService().createProductSlugLink(
            storeHandle: product.storehandle!,
            productSlug: product.productSlug!),
        imageLink: imageUrl,
        storeName: product.storeName,
        productName: product.productName,
        productBrand: product.brandName,
        storeIconUrl: product.storeIcon,
        objectReference: productReference,
      ),
      context: context,
    );
  }
//endregion

  //region Ask confirmation before delete
  void askConfirmBeforeDelete({required String productReference}) {
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Delete",
          isMessageVisible: true,
          popPreviousScreen: true,
          onTapSave: (v) {
            //Delete product
            SellerHideDeleteService().deleteProduct(productReference);
            //remove product from product dat model
            productDataModel.allProducts.removeWhere(
                (element) => element.productReference == productReference);
            // productList.removeWhere((element) => element.productReference == productReference);
            //Update ui
            productDataModel.updateUi();
            //Pop bottom sheet
            Navigator.pop(context);
            // requestForSaveVerificationType();
          },
          previousScreenContext: context,
        ));
  }
  //endregion

  //region Discount calculate
  double discountCalculate({required Product product}) {
    if (product.mrpPrice! <= 0 || product.sellingPrice! < 0) {
      throw ArgumentError(
          'MRP must be greater than 0 and Selling Price cannot be negative.');
    }
    double discount =
        ((product.mrpPrice! - product.sellingPrice!) / product.mrpPrice!) * 100;
    return double.parse(discount.toStringAsFixed(2));
  }
  //endregion

  //region Variant Management
  void initializeVariants() {
    try {
      // Check if we have specific variant details from order context
      if (product.variantDetailsOfOrder != null) {
        // Use the specific variant from order context
        selectedVariant = product.variantDetailsOfOrder;
        availableVariants = [product.variantDetailsOfOrder!];

        // Create options from the specific variant if needed
        if (product.options == null || product.options!.isEmpty) {
          if (product.variantDetailsOfOrder!.combinations.isNotEmpty) {
            product.options = product.variantDetailsOfOrder!.combinations.map(
              (key, value) => MapEntry(key, [value])
            );
          }
        }
        return;
      }

      // Fallback to regular variant handling
      if (product.variants != null && product.variants!.isNotEmpty) {
        // Convert variants from JSON
        availableVariants = product.variants!
            .map((variantJson) {
              try {
                return ProductVariant.fromJson(variantJson);
              } catch (e) {
                return null;
              }
            })
            .where((variant) => variant != null)
            .cast<ProductVariant>()
            .toList();

        if (availableVariants.isNotEmpty) {
          // Extract options from variants if not provided in product
          if (product.options == null || product.options!.isEmpty) {
            _extractOptionsFromVariants();
          }

          // Select first variant with combinations (since API variants always have combinations)
          selectedVariant = availableVariants.firstWhere(
            (variant) => variant.combinations.isNotEmpty,
            orElse: () => availableVariants.first,
          );
        }
      }
    } catch (e) {
      selectedVariant = null;
    }
  }

  void _extractOptionsFromVariants() {
    if (availableVariants.isEmpty) return;

    Map<String, Set<String>> optionsMap = {};

    // Collect all option keys and values from variants
    for (final variant in availableVariants) {
      variant.combinations.forEach((key, value) {
        if (!optionsMap.containsKey(key)) {
          optionsMap[key] = <String>{};
        }
        optionsMap[key]!.add(value);
      });
    }

    // Convert to the format expected by product.options
    if (optionsMap.isNotEmpty) {
      product.options = optionsMap.map((key, valueSet) =>
        MapEntry(key, valueSet.toList()));
    }
  }

  void selectVariant(ProductVariant variant) {
    selectedVariant = variant;
  }

  // Get display pricing from selected variant or fallback to product data
  int getDisplayMrpPrice() {
    if (selectedVariant != null) {
      return selectedVariant!.mrpPrice;
    }
    return product.mrpPrice ?? 999;
  }

  int getDisplaySellingPrice() {
    if (selectedVariant != null) {
      return selectedVariant!.sellingPrice;
    }
    return product.sellingPrice ?? 799;
  }

  int getDisplayStock() {
    if (selectedVariant != null) {
      return selectedVariant!.stock;
    }
    return product.inStock ?? 10;
  }

  double getDisplayDiscountPercentage() {
    int mrp = getDisplayMrpPrice();
    int selling = getDisplaySellingPrice();

    if (mrp <= 0 || selling < 0) {
      return 0.0;
    }

    double discount = ((mrp - selling) / mrp) * 100;
    return double.parse(discount.toStringAsFixed(0));
  }

  bool hasVariants() {
    // If we have specific variant details from order context, don't show variant selection
    if (product.variantDetailsOfOrder != null) {
      return false;
    }

    return availableVariants.where((v) => v.combinations.isNotEmpty).length > 1 ||
           (product.options != null && product.options!.isNotEmpty);
  }

  List<ProductVariant> getVariantsWithCombinations() {
    return availableVariants.where((variant) => variant.combinations.isNotEmpty).toList();
  }
  //endregion

// region Go to Single product screen
  void goToSingleProductScreen({required String productReference}) {
    var screen =
        BuyerViewSingleProductScreen(productReference: productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

// region Go to Full view
  void goToFullView({required Product product}) {
    // Set page URL for web app navigation using the same URL as sharing
    String productShareUrl = AppLinkCreateService()
        .createProductLink(productReference: product.productReference!);

    // Extract path from full URL for web navigation
    String urlPath = productShareUrl;
    try {
      Uri uri = Uri.parse(productShareUrl);
      urlPath = '${uri.path}${uri.query.isNotEmpty ? '?${uri.query}' : ''}';
    } catch (e) {
      // Use full URL if parsing fails
    }

    PageUrlService.setPageUrl(
        urlPath, '${product.productName} - ${product.storeName}');

    var screen = ProductFullView(product: product);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Convert date
  String convertDateFormat({required String inputDateTimeString}) {
    DateTime dateTime;

    try {
      // Check if data contain / then convert in different format
      if (inputDateTimeString.contains("/")) {
        dateTime = DateFormat("dd/MM/yyyy HH:mm:ss").parse(inputDateTimeString);
      } else if (inputDateTimeString.contains("T") ||
          inputDateTimeString.contains("+") ||
          inputDateTimeString.contains("Z")) {
        // Handle ISO 8601 format (e.g., "2025-06-10 21:09:49.307445+05:30" or "2025-06-10T21:09:49.307445+05:30")
        dateTime = DateTime.parse(inputDateTimeString);
      } else {
        dateTime = DateFormat('dd:MM:yyyy HH:mm:ss').parse(inputDateTimeString);
      }
    } catch (e) {
      // If parsing fails, try DateTime.parse as fallback
      try {
        dateTime = DateTime.parse(inputDateTimeString);
      } catch (e2) {
        // If all parsing fails, use current date as fallback
        dateTime = DateTime.now();
      }
    }

    // Formatting the date and time
    String formattedDateTime = DateFormat('h:mma MMM d, yyyy').format(dateTime);

    return formattedDateTime;
  }

  //endregion

  //region Repost
  Future<void> rePost({required Product product}) async {
    try {
      // Get reference to the product data model
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);
      //Get reference to Product data model
      // var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
      // //Logged in user data model
      // var loggedInUserInfoDataModel = Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
      // //SellerOwnStoreInfoDataModel store data model
      // var sellerOwnStoreInfoDataModel = Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);

      PostService().repost(
          postReference: product.productReference!,
          repostStatus:
              product.contentCategory == EntityType.REPOST.name ? false : true);

      //If post has already reposted then update the contentCategory to POST
      if (product.contentCategory == EntityType.REPOST.name) {
        productDataModel.allProducts
            .firstWhere((element) =>
                element.productReference == product.productReference)
            .contentCategory = EntityType.POST.name;
      } else {
        productDataModel.allProducts
            .firstWhere((element) =>
                element.productReference == product.productReference)
            .contentCategory = EntityType.REPOST.name;
        //Reposted toast message
        CommonMethods.toastMessage("Reposted", context, toastShowTimer: 5);
      }
      //Refresh ui
      productDataModel.updateUi();
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      return;
    }
  }
//endregion

  //region Save
  Future<void> save({required Product product}) async {
    try {
      // Get reference to the product data model
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      //If post has already saved
      if (product.saveStatus!) {
        productDataModel.allProducts
            .firstWhere((element) =>
                element.productReference == product.productReference)
            .saveStatus = false;
      } else {
        productDataModel.allProducts
            .firstWhere((element) =>
                element.productReference == product.productReference)
            .saveStatus = true;

        //Reposted toast message
        CommonMethods.toastMessage("Saved", context, toastShowTimer: 5);
      }
      //Refresh ui
      productDataModel.updateUi();

      PostService().repost(
          postReference: product.productReference!,
          repostStatus:
              product.contentCategory == EntityType.REPOST.name ? false : true);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      return;
    }
  }
//endregion

//region Switch image and story
  void switchImageAndStory({required bool value}) {
    flipController.toggleCard();
    switchProductImageAndStoryIconCtrl.sink.add(!value);
  }
//endregion

  //region On tap create external review link
  void onTapCreateExternalReviewLink(
      {required String productReference, required String? productImage}) {
    var screen = CreateExternalReviewRequestScreen(
      product: product,
      productImage: productImage,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion
}

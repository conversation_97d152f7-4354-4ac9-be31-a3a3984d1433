import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/product_slug_code_response/product_slug_code_response.dart';
import 'package:swadesic/services/product_slug_code_services/product_slug_code_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/my_reach_text_controller/my_reach_text_controller.dart';
import 'package:swadesic/features/seller/inventory_options/inventory_options_screen.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';

class AddEditProductFieldsBloc {
  // region Common Variables
  BuildContext context;
  bool isSellingPriceWarningVisible = false;
  bool isDiscountVisible = false;
  static bool _isAffiliatePromotionEnabled = false;
  bool isCommissionWarningVisible = false;
  static bool? isUrlValid;
  double discount = 0.0;
  double affiliateCommissionPercentage = 0.0;
  static String gender = "M";

  // Inventory options properties
  static Map<String, List<String>> productOptions = {};
  static List<ProductVariant> productVariants = [];
  static bool hasMultipleOptions = false;

  // Product slug/code availability variables
  late ProductSlugCodeServices productSlugCodeServices;
  bool? isProductSlugAvailable;
  bool? isProductCodeAvailable;
  Timer? _productSlugTimer;
  Timer? _productCodeTimer;

  // endregion

  //region Text Editing Controller
  static TextEditingController brandNameTextCtrl = TextEditingController();
  static TextEditingController productNameTextCtrl = TextEditingController();
  static TextEditingController productCategoryTextCtrl =
      TextEditingController();
  static TextEditingController promoLinkTextCtrl = TextEditingController();
  // static TextEditingController hashTagsTextCtrl = TextEditingController();
  static TextEditingController inStockTextCtrl = TextEditingController();
  static TextEditingController mrpTextCtrl = TextEditingController();
  static TextEditingController sellingPriceTextCtrl = TextEditingController();
  static TextEditingController affiliateCommissionTextCtrl =
      TextEditingController();
  static TextEditingController productSlugTextCtrl = TextEditingController();
  static TextEditingController productCodeTextCtrl = TextEditingController();
  // static TextEditingController productDescNameTextCtrl = TextEditingController();
  //endregion

  // Map<RegExp, TextStyle> pattern= {
  //   RegExp(AppConstants.acceptAll):const TextStyle(color:AppColors.brandGreen,fontFamily:AppConstants.rRegular, fontSize: 14,fontWeight: FontWeight.w400),
  // };

  static MyReachTextController hashTagsTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(AppConstants.hashTag):
        AppTextStyle.contentText0(textColor: AppColors.brandBlack),
  }, onMatch: (List<String> match) {});

  static MyReachTextController productDescNameTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(AppConstants.atTag):
        AppTextStyle.contentText0(textColor: AppColors.brandBlack),
  }, onMatch: (List<String> match) {});

  // static MyReachTextController productDescNameTextCtrl = MyReachTextController(
  //     patternMatchMap: {
  //       RegExp(AppConstants.acceptAll):
  //           const TextStyle(color: AppColors.brandGreen, fontFamily: AppConstants.rRegular, fontSize: 14, fontWeight: FontWeight.w400),
  //     },
  //     onMatch: (List<String> match) {
  //       Map<RegExp, TextStyle> pattern = {
  //         RegExp(AppConstants.acceptAll):
  //             const TextStyle(color: AppColors.brandGreen, fontFamily: AppConstants.rRegular, fontSize: 14, fontWeight: FontWeight.w400),
  //       };
  //     });

  //endregion

  //region Controller
  final imageCtrl = StreamController<bool>.broadcast();
  final productDiscountCtrl = StreamController<bool>.broadcast();
  final promotionLinkCtrl = StreamController<bool>.broadcast();
  final genderCtrl = StreamController<String>.broadcast();
  final affiliateCtrl = StreamController<bool>.broadcast();
  final productSlugAvailabilityCtrl = StreamController<bool>.broadcast();
  final productCodeAvailabilityCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  AddEditProductFieldsBloc(this.context);

  // endregion

  // region Init
  void init() {
    //
    // isUrlValid = CommonMethods.urlValidationCheck(url: promoLinkTextCtrl.text);
    productSlugCodeServices = ProductSlugCodeServices();
    onChangeSellingPrices();

    // Add listeners for product slug and code
    productSlugTextCtrl.addListener(() {
      onChangeProductSlug(value: productSlugTextCtrl.text);
    });

    productCodeTextCtrl.addListener(() {
      onChangeProductCode(value: productCodeTextCtrl.text);
    });
  }

// endregion

  //region On Select Gender
  void onSelectGender(String selectedGender) {
    gender = selectedGender;
    genderCtrl.sink.add(gender);
  }
//endregion

  //region On change selling prices
  onChangeSellingPrices() async {
    await Future.delayed(Duration.zero);

    //If any one of the field is empty
    if (AddEditProductFieldsBloc.mrpTextCtrl.text.isEmpty ||
        AddEditProductFieldsBloc.sellingPriceTextCtrl.text.isEmpty) {
      isSellingPriceWarningVisible = false;
      //Discount
      discount = 0.0;
      //Discount hide
      isDiscountVisible = false;

      //refresh
      productDiscountCtrl.sink.add(true);
      return;
    }
    //Check selling is greater then mrp
    if (double.parse(AddEditProductFieldsBloc.mrpTextCtrl.text) <
        double.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text)) {
      isSellingPriceWarningVisible = true;
      //Discount hide
      isDiscountVisible = false;
      //Discount
      discount = 0.0;
    } else {
      isSellingPriceWarningVisible = false;
      double mrp = double.parse(AddEditProductFieldsBloc.mrpTextCtrl.text);
      double selling =
          double.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text);
      //Discount
      discount = ((mrp - selling) / mrp) * 100;
      //Discount Visible
      isDiscountVisible = true;
    }
    //refresh
    productDiscountCtrl.sink.add(true);
  }
  //endregion

  //region Check url validation
  void checkUrlValidation() {
    //print(AddEditProductFieldsBloc.promoLinkTextCtrl.text);
    if (AddEditProductFieldsBloc.promoLinkTextCtrl.text.isEmpty) {
      isUrlValid = null;
    } else {
      isUrlValid = CommonMethods.urlValidationCheck(
          url: AddEditProductFieldsBloc.promoLinkTextCtrl.text);
    }
    //refresh
    promotionLinkCtrl.sink.add(true);
  }
//endregion

  // Getter for isAffiliatePromotionEnabled
  bool get isAffiliatePromotionEnabled => _isAffiliatePromotionEnabled;
  static bool get getIsAffiliatePromotionEnabled =>
      _isAffiliatePromotionEnabled;

  //region Toggle Affiliate
  void toggleAffiliate(bool value) {
    _isAffiliatePromotionEnabled = value;
    if (!value) {
      affiliateCommissionTextCtrl.clear();
      affiliateCommissionPercentage = 0.0;
      isCommissionWarningVisible = false;
    }
    affiliateCtrl.sink.add(_isAffiliatePromotionEnabled);
  }
  //endregion

  //region Calculate Affiliate Commission
  void calculateAffiliateCommission() {
    if (affiliateCommissionTextCtrl.text.isEmpty ||
        sellingPriceTextCtrl.text.isEmpty) {
      affiliateCommissionPercentage = 0.0;
      isCommissionWarningVisible = false;
      affiliateCtrl.sink.add(_isAffiliatePromotionEnabled);
      return;
    }

    double commission = double.parse(affiliateCommissionTextCtrl.text);
    double sellingPrice = double.parse(sellingPriceTextCtrl.text);

    // Check if commission is greater than selling price
    if (commission > sellingPrice) {
      isCommissionWarningVisible = true;
      affiliateCommissionPercentage = 0.0;
    } else {
      isCommissionWarningVisible = false;
      affiliateCommissionPercentage = (commission / sellingPrice) * 100;
    }
    affiliateCtrl.sink.add(_isAffiliatePromotionEnabled);
  }
  //endregion

  //region On change product slug
  void onChangeProductSlug({required String value}) {
    if (_productSlugTimer?.isActive ?? false) {
      _productSlugTimer!.cancel();
    }
    _productSlugTimer = Timer(const Duration(seconds: 1), () {
      checkProductSlugAvailability(value: value);
    });
  }
  //endregion

  //region On change product code
  void onChangeProductCode({required String value}) {
    if (_productCodeTimer?.isActive ?? false) {
      _productCodeTimer!.cancel();
    }
    _productCodeTimer = Timer(const Duration(seconds: 1), () {
      checkProductCodeAvailability(value: value);
    });
  }
  //endregion

  //region Check product slug availability
  checkProductSlugAvailability({required String value}) async {
    // If product slug field is empty then return
    if (value == "") {
      isProductSlugAvailable = null;
      productSlugAvailabilityCtrl.sink.add(true);
      return;
    }

    try {
      // API call to check product slug availability
      ProductSlugCodeAvailabilityResponse response = await productSlugCodeServices.checkProductSlugAvailability(
        storeReference: AppConstants.appData.storeReference!,
        searchQuery: value,
      );

      isProductSlugAvailable = response.available == "true" ? false : true; // true means available, false means not available
      productSlugAvailabilityCtrl.sink.add(true);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductSlug, context);
      isProductSlugAvailable = null;
      productSlugAvailabilityCtrl.sink.add(true);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductSlug, context);
      isProductSlugAvailable = null;
      productSlugAvailabilityCtrl.sink.add(true);
    }
  }
  //endregion

  //region Check product code availability
  checkProductCodeAvailability({required String value}) async {
    // If product code field is empty then return
    if (value == "") {
      isProductCodeAvailable = null;
      productCodeAvailabilityCtrl.sink.add(true);
      return;
    }

    try {
      // API call to check product code availability
      ProductSlugCodeAvailabilityResponse response = await productSlugCodeServices.checkProductCodeAvailability(
        storeReference: AppConstants.appData.storeReference!,
        searchQuery: value,
      );

      isProductCodeAvailable = response.available == "true" ? false : true; // true means available, false means not available
      productCodeAvailabilityCtrl.sink.add(true);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductCode, context);
      isProductCodeAvailable = null;
      productCodeAvailabilityCtrl.sink.add(true);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductCode, context);
      isProductCodeAvailable = null;
      productCodeAvailabilityCtrl.sink.add(true);
    }
  }
  //endregion

  //region Inventory Options Methods
  void goToInventoryOptionsScreen() {
    // Create a temporary product with current form data for inventory options
    Product tempProduct = Product(
      productReference: "cached_data", // Special reference to indicate cached data
      productName: productNameTextCtrl.text,
      brandName: brandNameTextCtrl.text,
      // mrpPrice: int.tryParse(mrpTextCtrl.text) ?? 0,
      // sellingPrice: int.tryParse(sellingPriceTextCtrl.text) ?? 0,
      // inStock: int.tryParse(inStockTextCtrl.text) ?? 0,
      options: productOptions.isNotEmpty ? productOptions : null,
      variants: productVariants.isNotEmpty
        ? productVariants.map((v) => v.toJson()).toList()
        : null,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryOptionsScreen(
          storeReference: AppConstants.appData.storeReference ?? "",
          product: tempProduct,
        ),
      ),
    ).then((result) {
      if (result != null && result is Map<String, dynamic>) {
        productOptions = Map<String, List<String>>.from(result['options'] ?? {});
        productVariants = List<ProductVariant>.from(result['variants'] ?? []);
        hasMultipleOptions = result['hasMultipleOptions'] ?? false;

        // Update text fields with variant data if no multiple options
        if (!hasMultipleOptions && productVariants.isNotEmpty) {
          final variant = productVariants.first;
          mrpTextCtrl.text = variant.mrpPrice.toString();
          sellingPriceTextCtrl.text = variant.sellingPrice.toString();
          inStockTextCtrl.text = variant.stock.toString();
        }
      }
    });
  }

  String getInventoryOptionsDisplayText() {
    if (hasMultipleOptions && productOptions.isNotEmpty) {
      int optionCount = productOptions.length;
      int variantCount = productVariants.length;
      return "$optionCount options, $variantCount variants";
    } else if (productVariants.isNotEmpty) {
      final variant = productVariants.first;
      return "MRP: ₹${variant.mrpPrice}, Selling: ₹${variant.sellingPrice}, Stock: ${variant.stock}";
    } else {
      // Fallback to text field values if no variants
      String mrp = mrpTextCtrl.text.isNotEmpty ? mrpTextCtrl.text : "0";
      String selling = sellingPriceTextCtrl.text.isNotEmpty ? sellingPriceTextCtrl.text : "0";
      String stock = inStockTextCtrl.text.isNotEmpty ? inStockTextCtrl.text : "0";
      return "MRP: ₹$mrp, Selling: ₹$selling, Stock: $stock";
    }
  }

  static void clearInventoryData() {
    productOptions.clear();
    productVariants.clear();
    hasMultipleOptions = false;
  }

  static void loadInventoryDataFromProduct(Map<String, List<String>>? options, List<Map<String, dynamic>>? variants) {
    if (options != null) {
      productOptions = Map.from(options);
    }

    if (variants != null && variants.isNotEmpty) {
      productVariants = variants
          .map((variantJson) {
            try {
              return ProductVariant.fromJson(variantJson);
            } catch (e) {
              return null;
            }
          })
          .where((variant) => variant != null)
          .cast<ProductVariant>()
          .toList();

      hasMultipleOptions = productOptions.isNotEmpty && productVariants.length > 1;

      // If we have variants but no options, extract options from variants
      if (productOptions.isEmpty && productVariants.isNotEmpty) {
        Map<String, Set<String>> optionsMap = {};
        for (final variant in productVariants) {
          variant.combinations.forEach((key, value) {
            if (!optionsMap.containsKey(key)) {
              optionsMap[key] = <String>{};
            }
            optionsMap[key]!.add(value);
          });
        }
        productOptions = optionsMap.map((key, valueSet) =>
          MapEntry(key, valueSet.toList()));
        hasMultipleOptions = productOptions.isNotEmpty && productVariants.length > 1;
      }
    }
  }
  //endregion

//region Dispose
  void dispose() {
    imageCtrl.close();
    productDiscountCtrl.close();
    promotionLinkCtrl.close();
    genderCtrl.close();
    affiliateCtrl.close();
    productSlugAvailabilityCtrl.close();
    productCodeAvailabilityCtrl.close();
    _productSlugTimer?.cancel();
    _productCodeTimer?.cancel();
  }
  //endregion
}
